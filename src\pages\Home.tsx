import { Button } from '@/components/ui/button';
import { MapPin, MapPinHouse, MessageSquare, Search, User } from 'lucide-react';
import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Address, useGetProximityNursesQuery } from '@/store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import { useAddressSelection } from '@/hooks/useAddressSelection';
import NotificationBadge from '@/components/NotificationBadge';
import { useUnreadMessages } from '@/hooks/useUnreadMessages';
import Footer from '@/components/Footer';
import { useHomeData } from '../hooks/useHomeData';
import { useAddressDropdown } from '../hooks/useAddressDropdown';
import { useScreenSize } from '../hooks/useScreenSize';
import AddressDropdown from '../components/AddressDropdown';
import BookingsSection from '../components/BookingsSection';
import NursesSection from '../components/NursesSection';

function Home() {
  const { unreadCount, refetch } = useUnreadMessages();
  const navigate = useNavigate();
  const [selectedRadius, setSelectedRadius] = useState<number>(7);
  const { mobileView } = useScreenSize();
  const homeData = useHomeData();
  const {
    username,
    given_name,
    address,
    profile,
    profileLoading,
    profileError,
    addressesResponse,
    addressesLoading,
    addressesError,
    refetchAddresses,
    bookingResponse,
    bookingLoading,
    bookingError,
    customerName,
    customerId,
    profileDetailsLoading,
  } = homeData;

  const addressDropdown = useAddressDropdown(addressesResponse);
  const {
    showAddressDropdown,
    onOpenRef,
    addressButtonRefs,
    handleDropdownKeyDown,
    toggleDropdown,
    closeDropdown,
  } = addressDropdown;

  const {
    selectedAddress,
    selectedAddressId,
    isInitialized,
    selectAddress,
    resetAddressSelection,
  } = useAddressSelection(
    addressesResponse?.data && Array.isArray(addressesResponse.data)
      ? addressesResponse.data
      : undefined
  );

  // Enhanced refetch functionality for unread count
  const refetchUnreadCount = useCallback(() => {
    refetch();
  }, [refetch]);

  // Refetch on window focus
  useEffect(() => {
    const handleFocus = () => {
      refetchUnreadCount();
    };
    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchUnreadCount]);

  const {
    data: nursesResponse,
    isLoading: nursesLoading,
    error: nursesError,
  } = useGetProximityNursesQuery(
    {
      customerId,
      radius: selectedRadius,
      addressId: selectedAddressId,
    },
    {
      skip: !username || !customerId || !selectedAddressId,
      refetchOnMountOrArgChange: true,
    }
  );

  const handleAddressSelect = (address: Address) => {
    selectAddress(address);
    closeDropdown();
  };

  const handleAddressRetry = () => {
    refetchAddresses();
  };

  useEffect(() => {
    if (addressesResponse?.data && Array.isArray(addressesResponse.data)) {
      const currentAddresses = addressesResponse.data;

      if (
        selectedAddress &&
        !currentAddresses.find(
          (addr: Address) => addr.id === selectedAddress.id
        )
      ) {
        resetAddressSelection();
      }
    }
  }, [addressesResponse?.data, selectedAddress, resetAddressSelection]);

  if (
    profileLoading ||
    addressesLoading ||
    nursesLoading ||
    profileDetailsLoading ||
    bookingLoading ||
    !isInitialized
  ) {
    return (
      <div>
        <ResponsiveLoader />
      </div>
    );
  }

  if (profileError) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <div className='text-red-600 text-lg mb-4'>Error loading profile</div>
          <Button
            onClick={() => window.location.reload()}
            className='px-4 py-2 bg-nursery-darkBlue text-white rounded-md hover:bg-opacity-80'
          >
            Reload Page
          </Button>
        </div>
      </div>
    );
  }

  const displayName = given_name || profile?.data?.given_name || 'User';

  let displayAddress = 'Address not available';
  let displayAddressName = 'HOME';
  let addressIcon = MapPinHouse;

  if (selectedAddress) {
    displayAddress = selectedAddress.address;
    displayAddressName = selectedAddress.name;
    addressIcon = selectedAddress.icon === 'home' ? MapPinHouse : MapPin;
  } else if (address) {
    displayAddress = address;
  } else if (profile?.data?.address) {
    displayAddress = profile.data.address;
  }

  const hasAddresses =
    addressesResponse?.data &&
    Array.isArray(addressesResponse.data) &&
    addressesResponse.data.length > 0;

  return (
    <>
      <div>
        <header className='w-full'>
          <div
            className='flex justify-between py-6 md:px-6 px-6 items-center max-w-full bg-white'
            style={{
              backgroundImage: `url(/Images/bg4.png)`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <div className='w-full mx-auto md:w-10/12 flex justify-between items-center'>
              <div>
                {mobileView ? (
                  <img
                    src='/Images/whiteicon.svg'
                    alt='Company Logo'
                    className='logo logo-mobile w-[45px] object-cover'
                  />
                ) : (
                  <img
                    src='/Images/Logo.svg'
                    alt='Company Logo'
                    className='logo logo-desktop w-[155px] object-cover'
                  />
                )}
              </div>
              <div className='flex flex-col md:flex-row md:items-center gap-2'>
                <AddressDropdown
                  selectedAddress={selectedAddress}
                  displayAddress={displayAddress}
                  displayAddressName={displayAddressName}
                  addressIcon={addressIcon}
                  hasAddresses={hasAddresses}
                  addressesResponse={addressesResponse}
                  showAddressDropdown={showAddressDropdown}
                  onToggleDropdown={toggleDropdown}
                  onCloseDropdown={closeDropdown}
                  onAddressSelect={handleAddressSelect}
                  onDropdownKeyDown={handleDropdownKeyDown}
                  onOpenRef={onOpenRef}
                  addressButtonRefs={addressButtonRefs}
                  addressesError={addressesError}
                  onAddressRetry={handleAddressRetry}
                  addressesLoading={addressesLoading}
                />
                <div className='hidden md:flex md:items-center gap-6'>
                  <button
                    className='cursor-pointer p-2 bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 rounded'
                    onClick={() => {
                      // Refetch unread count before navigating to ensure fresh data
                      refetchUnreadCount();
                      navigate('/chat');
                    }}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        refetchUnreadCount();
                        navigate('/chat');
                      }
                    }}
                    aria-label={`Go to chat${unreadCount > 0 ? ` (${unreadCount} unread messages)` : ''}`}
                  >
                    <div className='relative'>
                      <MessageSquare className='h-7 w-7 text-white' />
                      <NotificationBadge count={unreadCount} />
                    </div>
                  </button>
                  <button
                    className='cursor-pointer p-2 bg-white rounded-full'
                    onClick={() => navigate('/profile')}
                  >
                    <span>
                      <User className='text-nursery-blue' />
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {}
        <main className='flex-1 overflow-hidden bg-white pb-[75px] md:pb-5'>
          <div className='flex flex-1 flex-col p-4 mx-auto md:w-10/12 w-11/12 '>
            <section className='relative flex flex-row justify-between items-center gap-6 w-full'>
              {}
              <div className='flex flex-col md:mt-3'>
                <h1 className='text-xl font-bold text-gray-800'>
                  Welcome! <em className='text-nursery-blue'>{displayName}</em>
                </h1>
                <p className='text-base font-semibold pb-2 text-gray-800'>
                  How is it going today?
                </p>
              </div>
              {}
              <div className='absolute left-1/2 top-full mt-4 transform -translate-x-1/2 w-full md:static md:transform-none md:mt-0 md:w-[65%]'>
                <div className='bg-[#F2F2F2] px-4 py-2 flex flex-row rounded-xl gap-2 items-center transition-all duration-500 ease-in-out'>
                  <div className='rounded-2xl'>
                    <Search size={24} strokeWidth='2px' />
                  </div>
                  <input
                    type='text'
                    className='w-full bg-transparent border-0 shadow-none outline-none ring-0 placeholder:text-base placeholder:text-slate-500 focus:outline-none focus:ring-0 focus:border-0 focus:shadow-none'
                    placeholder='Search a nurse name...'
                  />
                </div>
              </div>
            </section>

            <div className='md:pt-2 pt-8 mt-1'>
              <BookingsSection
                bookingResponse={bookingResponse}
                bookingLoading={bookingLoading}
                bookingError={bookingError}
              />
            </div>

            <div className='md:pt-2 pt-0'>
              <NursesSection
                nursesResponse={nursesResponse}
                nursesLoading={nursesLoading}
                nursesError={nursesError}
                selectedRadius={selectedRadius}
                onRadiusChange={setSelectedRadius}
                customerId={customerId}
                customerName={customerName}
                selectedAddress={selectedAddress}
              />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
}

export default Home;
